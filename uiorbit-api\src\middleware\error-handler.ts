import { Request, Response, NextFunction } from 'express'
import { logger } from '../utils/logger'

export interface APIError extends Error {
  statusCode?: number
  code?: string
}

export function errorHandler(
  error: APIError,
  req: Request,
  res: Response,
  next: NextFunction
) {
  // Log the error
  logger.error('API Error', {
    error: error.message,
    stack: error.stack,
    url: req.url,
    method: req.method,
    ip: req.ip,
    userAgent: req.get('User-Agent')
  })

  // Default error response
  let statusCode = error.statusCode || 500
  let message = error.message || 'Internal Server Error'
  let code = error.code || 'INTERNAL_ERROR'

  // Handle specific error types
  if (error.name === 'ValidationError') {
    statusCode = 400
    message = 'Validation failed'
    code = 'VALIDATION_ERROR'
  } else if (error.name === 'UnauthorizedError') {
    statusCode = 401
    message = 'Unauthorized'
    code = 'UNAUTHORIZED'
  } else if (error.name === 'ForbiddenError') {
    statusCode = 403
    message = 'Forbidden'
    code = 'FORBIDDEN'
  } else if (error.name === 'NotFoundError') {
    statusCode = 404
    message = 'Not found'
    code = 'NOT_FOUND'
  } else if (error.name === 'RateLimitError') {
    statusCode = 429
    message = 'Too many requests'
    code = 'RATE_LIMIT_EXCEEDED'
  }

  // Don't expose internal errors in production
  if (process.env.NODE_ENV === 'production' && statusCode === 500) {
    message = 'Internal Server Error'
  }

  res.status(statusCode).json({
    success: false,
    error: {
      code,
      message,
      ...(process.env.NODE_ENV === 'development' && { stack: error.stack })
    }
  })
}
