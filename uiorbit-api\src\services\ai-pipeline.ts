import { OpenAI } from 'openai'
import Anthropic from '@anthropic-ai/sdk'
import { logger } from '../utils/logger'

export interface UIGenerationRequest {
  prompt: string
  framework: string
  styling: string
  context: any
  userId: string
}

export interface UIGenerationResult {
  code: string
  componentName: string
  description: string
  usage: string
  model: string
  qualityScore: number
  suggestions: string[]
  metadata: {
    framework: string
    styling: string
    accessibility: boolean
    responsive: boolean
    performance: number
  }
}

export class AIPipeline {
  private openai: OpenAI
  private anthropic: Anthropic

  constructor() {
    this.openai = new OpenAI({
      apiKey: process.env.OPENAI_API_KEY,
    })

    this.anthropic = new Anthropic({
      apiKey: process.env.ANTHROPIC_API_KEY,
    })
  }

  async generateUI(request: UIGenerationRequest): Promise<UIGenerationResult> {
    try {
      // Step 1: Create enhanced prompt with context
      const enhancedPrompt = this.createEnhancedPrompt(request)

      // Step 2: Generate code using multi-model approach
      const generationResult = await this.generateWithFallback(enhancedPrompt)

      // Step 3: Parse and validate the generated code
      const parsedResult = this.parseGeneratedCode(generationResult.content)

      // Step 4: Quality assurance and validation
      const qualityAssessment = await this.assessQuality(parsedResult.code, request.framework)

      // Step 5: Generate suggestions for improvement
      const suggestions = await this.generateSuggestions(parsedResult.code, qualityAssessment)

      return {
        ...parsedResult,
        model: generationResult.model,
        qualityScore: qualityAssessment.overallScore,
        suggestions,
        metadata: {
          framework: request.framework,
          styling: request.styling,
          accessibility: qualityAssessment.accessibility > 7,
          responsive: qualityAssessment.responsive > 7,
          performance: qualityAssessment.performance
        }
      }

    } catch (error) {
      logger.error('AI Pipeline generation failed', { error, userId: request.userId })
      throw new Error('Failed to generate UI component')
    }
  }

  private createEnhancedPrompt(request: UIGenerationRequest): string {
    const { prompt, framework, styling, context } = request

    return `# UIOrbit - Frontend AI Assistant

## User Request
${prompt}

## Technical Specifications
- **Framework**: ${framework}
- **Styling Framework**: ${styling}
- **Accessibility**: WCAG AA compliant with proper ARIA labels
- **Responsive Design**: Mobile-first approach with breakpoints
- **Performance**: Optimized for Core Web Vitals
- **Modern Standards**: Latest ${framework} best practices

## Project Context
${context.currentFile ? `- Current file: ${context.currentFile.path}` : ''}
${context.project ? `- Project framework: ${context.project.framework}` : ''}
${context.project ? `- Project styling: ${context.project.stylingFramework}` : ''}
${context.project ? `- Design system: ${context.project.designSystem}` : ''}
${context.suggestions ? `- Suggestions: ${context.suggestions.join(', ')}` : ''}

## Code Generation Requirements
1. **Clean Architecture**: Well-structured, maintainable code
2. **TypeScript**: Include proper type definitions
3. **Accessibility**: Semantic HTML, ARIA labels, keyboard navigation
4. **Responsive**: Mobile-first design with proper breakpoints
5. **Performance**: Optimized rendering and minimal re-renders
6. **Best Practices**: Follow ${framework} conventions and patterns
7. **Styling**: Use ${styling} effectively with consistent design tokens
8. **Error Handling**: Include proper error boundaries and validation

## Output Format
Please provide:
1. Complete component implementation
2. TypeScript interfaces/types
3. Usage example
4. Brief description of the component's purpose

Generate production-ready, accessible, and performant code.`
  }

  private async generateWithFallback(prompt: string): Promise<{ content: string; model: string }> {
    // Try Claude Sonnet 4 first (better for code generation)
    try {
      const claudeResponse = await this.anthropic.messages.create({
        model: 'claude-3-sonnet-20240229',
        max_tokens: 4000,
        messages: [{
          role: 'user',
          content: prompt
        }]
      })

      const content = claudeResponse.content[0].type === 'text' 
        ? claudeResponse.content[0].text 
        : 'Error generating code'

      return { content, model: 'claude-3-sonnet' }

    } catch (claudeError) {
      logger.warn('Claude API failed, falling back to OpenAI', { error: claudeError })

      // Fallback to OpenAI GPT-4
      const openaiResponse = await this.openai.chat.completions.create({
        model: 'gpt-4-turbo-preview',
        messages: [{
          role: 'user',
          content: prompt
        }],
        max_tokens: 4000,
        temperature: 0.7,
      })

      const content = openaiResponse.choices[0]?.message?.content || 'Error generating code'
      return { content, model: 'gpt-4-turbo' }
    }
  }

  private parseGeneratedCode(response: string) {
    // Extract code blocks
    const codeBlockRegex = /```(?:tsx?|jsx?|vue|svelte)?\n([\s\S]*?)```/g
    const matches = [...response.matchAll(codeBlockRegex)]
    
    const code = matches.length > 0 ? matches[0][1].trim() : response
    
    // Extract component name
    const nameMatch = response.match(/(?:export\s+(?:default\s+)?(?:function|const|class)\s+)(\w+)/i)
    const componentName = nameMatch ? nameMatch[1] : 'GeneratedComponent'
    
    // Extract description
    const descriptionMatch = response.split('```')[0].trim()
    
    // Extract usage example
    const usageMatch = response.match(/(?:usage|example)[\s\S]*?```(?:tsx?|jsx?)?\n([\s\S]*?)```/i)
    const usage = usageMatch ? usageMatch[1].trim() : ''

    return {
      code,
      componentName,
      description: descriptionMatch,
      usage
    }
  }

  private async assessQuality(code: string, framework: string): Promise<{
    overallScore: number
    accessibility: number
    responsive: number
    performance: number
    codeQuality: number
  }> {
    try {
      const assessmentPrompt = `
Analyze this ${framework} component code and provide quality scores (1-10):

\`\`\`${framework.toLowerCase()}
${code}
\`\`\`

Evaluate:
1. Accessibility (ARIA labels, semantic HTML, keyboard navigation)
2. Responsive design (mobile-first, breakpoints, flexible layouts)
3. Performance (efficient rendering, minimal re-renders, optimizations)
4. Code quality (structure, readability, best practices)

Respond with JSON only:
{
  "accessibility": number,
  "responsive": number,
  "performance": number,
  "codeQuality": number,
  "overallScore": number
}
      `

      const response = await this.openai.chat.completions.create({
        model: 'gpt-4-turbo-preview',
        messages: [{ role: 'user', content: assessmentPrompt }],
        max_tokens: 500,
        temperature: 0.1,
      })

      const assessment = JSON.parse(response.choices[0]?.message?.content || '{}')
      
      return {
        overallScore: assessment.overallScore || 7,
        accessibility: assessment.accessibility || 7,
        responsive: assessment.responsive || 7,
        performance: assessment.performance || 7,
        codeQuality: assessment.codeQuality || 7
      }

    } catch (error) {
      logger.warn('Quality assessment failed, using default scores', { error })
      return {
        overallScore: 7,
        accessibility: 7,
        responsive: 7,
        performance: 7,
        codeQuality: 7
      }
    }
  }

  private async generateSuggestions(code: string, quality: any): Promise<string[]> {
    const suggestions: string[] = []

    if (quality.accessibility < 8) {
      suggestions.push('Consider adding more ARIA labels and semantic HTML elements')
    }

    if (quality.responsive < 8) {
      suggestions.push('Improve responsive design with better breakpoint handling')
    }

    if (quality.performance < 8) {
      suggestions.push('Optimize for performance with memoization and efficient rendering')
    }

    if (quality.codeQuality < 8) {
      suggestions.push('Refactor for better code structure and readability')
    }

    // Add general best practice suggestions
    if (!code.includes('aria-')) {
      suggestions.push('Add ARIA attributes for better accessibility')
    }

    if (!code.includes('responsive') && !code.includes('sm:') && !code.includes('md:')) {
      suggestions.push('Add responsive design classes for mobile compatibility')
    }

    return suggestions.slice(0, 5) // Limit to 5 suggestions
  }
}
