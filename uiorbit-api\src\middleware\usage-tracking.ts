import { Response, NextFunction } from 'express'
import { createClient } from '@supabase/supabase-js'
import { AuthenticatedRequest } from './auth'
import { logger } from '../utils/logger'

const supabase = createClient(
  process.env.SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
)

export async function trackUsage(
  req: AuthenticatedRequest,
  res: Response,
  next: NextFunction
) {
  try {
    const userId = req.user?.id
    const userTier = req.user?.tier || 'free'

    if (!userId) {
      return res.status(401).json({ error: 'Authentication required' })
    }

    // Check monthly usage limits
    const currentMonth = new Date().toISOString().slice(0, 7) // YYYY-MM format
    
    const { data: monthlyUsage, error: usageError } = await supabase
      .from('user_usage')
      .select('*')
      .eq('user_id', userId)
      .eq('month', currentMonth)
      .single()

    if (usageError && usageError.code !== 'PGRST116') { // PGRST116 = no rows found
      logger.error('Failed to check usage', { error: usageError, userId })
      return res.status(500).json({ error: 'Failed to check usage limits' })
    }

    // Get tier limits
    const tierLimits = {
      free: 100,
      pro: 10000,
      enterprise: 100000
    }

    const monthlyLimit = tierLimits[userTier]
    const currentUsage = monthlyUsage?.requests_count || 0

    // Check if user has exceeded monthly limit
    if (currentUsage >= monthlyLimit) {
      logger.warn('Monthly usage limit exceeded', { userId, userTier, currentUsage, monthlyLimit })
      
      return res.status(429).json({
        error: 'Monthly usage limit exceeded',
        currentUsage,
        monthlyLimit,
        tier: userTier,
        upgradeUrl: userTier === 'free' ? '/upgrade' : '/contact'
      })
    }

    // Update usage count
    if (monthlyUsage) {
      await supabase
        .from('user_usage')
        .update({ 
          requests_count: currentUsage + 1,
          updated_at: new Date().toISOString()
        })
        .eq('id', monthlyUsage.id)
    } else {
      await supabase
        .from('user_usage')
        .insert({
          user_id: userId,
          month: currentMonth,
          requests_count: 1,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        })
    }

    // Add usage info to request for response
    req.usage = {
      currentUsage: currentUsage + 1,
      monthlyLimit,
      tier: userTier,
      remaining: monthlyLimit - (currentUsage + 1)
    }

    next()

  } catch (error) {
    logger.error('Usage tracking error', { error })
    // Allow request if usage tracking fails (fail open)
    next()
  }
}

// Middleware to add usage info to response headers
export function addUsageHeaders(
  req: AuthenticatedRequest,
  res: Response,
  next: NextFunction
) {
  const originalJson = res.json

  res.json = function(data: any) {
    if (req.usage) {
      res.set({
        'X-Usage-Current': req.usage.currentUsage.toString(),
        'X-Usage-Limit': req.usage.monthlyLimit.toString(),
        'X-Usage-Remaining': req.usage.remaining.toString(),
        'X-Usage-Tier': req.usage.tier
      })
    }

    return originalJson.call(this, data)
  }

  next()
}

// Extend the AuthenticatedRequest interface
declare module './auth' {
  interface AuthenticatedRequest {
    usage?: {
      currentUsage: number
      monthlyLimit: number
      tier: string
      remaining: number
    }
  }
}
