import { Request, Response, NextFunction } from 'express'
import { createClient } from '@supabase/supabase-js'
import { AuthenticatedRequest } from './auth'
import { logger } from '../utils/logger'

const supabase = createClient(
  process.env.SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
)

// Rate limiting based on user tier
const TIER_LIMITS = {
  free: {
    requestsPerMinute: 5,
    requestsPerHour: 50,
    requestsPerMonth: 100
  },
  pro: {
    requestsPerMinute: 20,
    requestsPerHour: 500,
    requestsPerMonth: 10000
  },
  enterprise: {
    requestsPerMinute: 100,
    requestsPerHour: 5000,
    requestsPerMonth: 100000
  }
}

export function rateLimitByUser(requestsPerMinute: number, windowMinutes: number = 1) {
  return async (req: AuthenticatedRequest, res: Response, next: NextFunction) => {
    try {
      const userId = req.user?.id
      const userTier = req.user?.tier || 'free'

      if (!userId) {
        return res.status(401).json({ error: 'Authentication required' })
      }

      // Get tier-specific limits
      const tierLimits = TIER_LIMITS[userTier]
      const effectiveLimit = Math.min(requestsPerMinute, tierLimits.requestsPerMinute)

      // Check rate limit in database
      const windowStart = new Date(Date.now() - windowMinutes * 60 * 1000)
      
      const { data: recentRequests, error } = await supabase
        .from('api_requests')
        .select('id')
        .eq('user_id', userId)
        .gte('created_at', windowStart.toISOString())

      if (error) {
        logger.error('Failed to check rate limit', { error, userId })
        // Allow request if we can't check (fail open)
        return next()
      }

      if (recentRequests.length >= effectiveLimit) {
        logger.warn('Rate limit exceeded', { userId, userTier, requests: recentRequests.length, limit: effectiveLimit })
        
        return res.status(429).json({
          error: 'Rate limit exceeded',
          limit: effectiveLimit,
          windowMinutes,
          retryAfter: windowMinutes * 60
        })
      }

      // Log the request
      await supabase
        .from('api_requests')
        .insert({
          user_id: userId,
          endpoint: req.path,
          method: req.method,
          created_at: new Date().toISOString()
        })

      next()

    } catch (error) {
      logger.error('Rate limiting error', { error })
      // Allow request if rate limiting fails (fail open)
      next()
    }
  }
}
