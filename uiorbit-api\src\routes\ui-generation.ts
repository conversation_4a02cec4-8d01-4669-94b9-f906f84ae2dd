import express from 'express'
import { body, validationResult } from 'express-validator'
import { OpenAI } from 'openai'
import Anthropic from '@anthropic-ai/sdk'
import { logger } from '../utils/logger'
import { rateLimitByUser } from '../middleware/rate-limit'
import { trackUsage } from '../middleware/usage-tracking'

const router = express.Router()

// Initialize AI clients
const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY,
})

const anthropic = new Anthropic({
  apiKey: process.env.ANTHROPIC_API_KEY,
})

// UI Generation endpoint
router.post('/generate',
  rateLimitByUser(10, 60), // 10 requests per minute per user
  [
    body('prompt').isString().isLength({ min: 10, max: 2000 }).trim(),
    body('framework').optional().isIn(['React', 'Vue', 'Angular', 'Svelte', 'HTML']),
    body('styling').optional().isIn(['Tailwind', 'CSS', 'Styled Components', 'Emotion']),
    body('context').optional().isObject(),
  ],
  trackUsage,
  async (req, res) => {
    try {
      // Validation
      const errors = validationResult(req)
      if (!errors.isEmpty()) {
        return res.status(400).json({ errors: errors.array() })
      }

      const { prompt, framework = 'React', styling = 'Tailwind', context = {} } = req.body
      const userId = req.user?.id

      logger.info(`UI generation request from user ${userId}`, { prompt, framework, styling })

      // Create enhanced prompt with context
      const enhancedPrompt = createEnhancedPrompt(prompt, framework, styling, context)

      // Generate UI using Claude Sonnet 4 (primary) with OpenAI fallback
      let generatedCode: string
      let model: string

      try {
        // Try Claude Sonnet 4 first (better for code generation)
        const claudeResponse = await anthropic.messages.create({
          model: 'claude-3-sonnet-20240229',
          max_tokens: 4000,
          messages: [{
            role: 'user',
            content: enhancedPrompt
          }]
        })

        generatedCode = claudeResponse.content[0].type === 'text' 
          ? claudeResponse.content[0].text 
          : 'Error generating code'
        model = 'claude-3-sonnet'

      } catch (claudeError) {
        logger.warn('Claude API failed, falling back to OpenAI', { error: claudeError })

        // Fallback to OpenAI GPT-4
        const openaiResponse = await openai.chat.completions.create({
          model: 'gpt-4-turbo-preview',
          messages: [{
            role: 'user',
            content: enhancedPrompt
          }],
          max_tokens: 4000,
          temperature: 0.7,
        })

        generatedCode = openaiResponse.choices[0]?.message?.content || 'Error generating code'
        model = 'gpt-4-turbo'
      }

      // Extract code blocks and metadata
      const result = parseGeneratedResponse(generatedCode)

      // Log successful generation
      logger.info(`UI generated successfully for user ${userId}`, { 
        model, 
        framework, 
        styling,
        codeLength: result.code.length 
      })

      res.json({
        success: true,
        data: {
          ...result,
          model,
          framework,
          styling,
          timestamp: new Date().toISOString()
        }
      })

    } catch (error) {
      logger.error('UI generation failed', { error, userId: req.user?.id })
      res.status(500).json({
        success: false,
        error: 'Failed to generate UI component'
      })
    }
  }
)

// Component analysis endpoint
router.post('/analyze',
  rateLimitByUser(20, 60), // 20 requests per minute per user
  [
    body('code').isString().isLength({ min: 10, max: 10000 }).trim(),
    body('framework').optional().isIn(['React', 'Vue', 'Angular', 'Svelte']),
  ],
  async (req, res) => {
    try {
      const errors = validationResult(req)
      if (!errors.isEmpty()) {
        return res.status(400).json({ errors: errors.array() })
      }

      const { code, framework = 'React' } = req.body
      const userId = req.user?.id

      // Analyze the component using AI
      const analysisPrompt = `
Analyze this ${framework} component and provide insights:

\`\`\`${framework.toLowerCase()}
${code}
\`\`\`

Please provide:
1. Component complexity score (1-10)
2. Accessibility score (1-10)
3. Performance score (1-10)
4. Responsive design score (1-10)
5. Suggestions for improvement
6. Detected patterns and best practices

Format as JSON.
      `

      const response = await openai.chat.completions.create({
        model: 'gpt-4-turbo-preview',
        messages: [{ role: 'user', content: analysisPrompt }],
        max_tokens: 1000,
        temperature: 0.3,
      })

      const analysis = response.choices[0]?.message?.content || '{}'
      
      logger.info(`Component analyzed for user ${userId}`, { framework })

      res.json({
        success: true,
        data: {
          analysis: JSON.parse(analysis),
          framework,
          timestamp: new Date().toISOString()
        }
      })

    } catch (error) {
      logger.error('Component analysis failed', { error, userId: req.user?.id })
      res.status(500).json({
        success: false,
        error: 'Failed to analyze component'
      })
    }
  }
)

// Helper functions
function createEnhancedPrompt(prompt: string, framework: string, styling: string, context: any): string {
  return `
# UIOrbit - Frontend AI Assistant

## User Request
${prompt}

## Technical Requirements
- **Framework**: ${framework}
- **Styling**: ${styling}
- **Accessibility**: WCAG AA compliant
- **Responsive**: Mobile-first design
- **Modern**: Use latest best practices

## Project Context
${context.projectInfo ? `- Project: ${context.projectInfo}` : ''}
${context.existingComponents ? `- Existing components: ${context.existingComponents.join(', ')}` : ''}
${context.designSystem ? `- Design system: ${context.designSystem}` : ''}

## Output Requirements
1. Generate clean, production-ready code
2. Include TypeScript types if applicable
3. Add proper ARIA labels and semantic HTML
4. Ensure mobile-first responsive design
5. Follow ${framework} best practices
6. Use ${styling} for styling
7. Include brief usage example

Please provide the complete component implementation.
  `
}

function parseGeneratedResponse(response: string) {
  // Extract code blocks
  const codeBlockRegex = /```(?:tsx?|jsx?|vue|svelte)?\n([\s\S]*?)```/g
  const matches = [...response.matchAll(codeBlockRegex)]
  
  const code = matches.length > 0 ? matches[0][1].trim() : response
  
  // Extract component name
  const nameMatch = response.match(/(?:export\s+(?:default\s+)?(?:function|const|class)\s+)(\w+)/i)
  const componentName = nameMatch ? nameMatch[1] : 'GeneratedComponent'
  
  return {
    code,
    componentName,
    description: response.split('```')[0].trim(),
    usage: extractUsageExample(response)
  }
}

function extractUsageExample(response: string): string {
  const usageMatch = response.match(/(?:usage|example)[\s\S]*?```(?:tsx?|jsx?)?\n([\s\S]*?)```/i)
  return usageMatch ? usageMatch[1].trim() : ''
}

export default router
