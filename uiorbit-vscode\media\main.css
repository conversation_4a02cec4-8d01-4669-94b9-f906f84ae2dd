:root {
    --container-padding: 0;
    --input-padding-vertical: 12px;
    --input-padding-horizontal: 16px;
    --input-margin-vertical: 8px;
    --input-margin-horizontal: 0;
    --border-radius: 8px;
    --border-radius-lg: 12px;
    --spacing-xs: 4px;
    --spacing-sm: 8px;
    --spacing-md: 16px;
    --spacing-lg: 24px;
    --spacing-xl: 32px;
}

body {
    padding: 0;
    margin: 0;
    color: var(--vscode-foreground);
    font-size: var(--vscode-editor-font-size);
    font-weight: var(--vscode-font-weight);
    font-family: var(--vscode-font-family);
    background-color: var(--vscode-editor-background);
    height: 100vh;
    overflow: hidden;
}

ol,
ul {
    padding-left: var(--container-padding);
}

body>*,
form>* {
    margin-block-start: var(--input-margin-vertical);
    margin-block-end: var(--input-margin-vertical);
}

*:focus {
    outline-color: var(--vscode-focusBorder) !important;
}

a {
    color: var(--vscode-textLink-foreground);
}

a:hover,
a:active {
    color: var(--vscode-textLink-activeForeground);
}

blockquote,
dd,
dl,
figure,
h1,
h3,
h4,
h5,
h6,
hr,
p {
    margin-block-start: 1em !important;
    margin-block-end: 1em !important;
    margin-inline-start: 0px !important;
    margin-inline-end: 0px !important;
}

h1 {
    font-size: 1.17em !important;
    margin-top: 0.67em !important;
    margin-bottom: 0.67em !important;
    margin-left: 0 !important;
    margin-right: 0 !important;
    font-weight: bold !important;
}

h2 {
    font-size: 1em !important;
    margin-top: 0.83em !important;
    margin-bottom: 0.83em !important;
    margin-left: 0 !important;
    margin-right: 0 !important;
    font-weight: bold !important;
}

h3 {
    font-size: .93em !important;
    margin-top: 1em !important;
    margin-bottom: 1em !important;
    margin-left: 0 !important;
    margin-right: 0 !important;
    font-weight: bold !important;
}

h4 {
    font-size: .85em !important;
    margin-top: 1.33em !important;
    margin-bottom: 1.33em !important;
    margin-left: 0 !important;
    margin-right: 0 !important;
    font-weight: bold !important;
}

h5 {
    font-size: .83em !important;
    margin-top: 1.67em !important;
    margin-bottom: 1.67em !important;
    margin-left: 0 !important;
    margin-right: 0 !important;
    font-weight: bold !important;
}

h6 {
    font-size: .8em !important;
    margin-top: 2.33em !important;
    margin-bottom: 2.33em !important;
    margin-left: 0 !important;
    margin-right: 0 !important;
    font-weight: bold !important;
}

code {
    font-family: var(--vscode-editor-font-family) !important;
}

button {
    border: none;
    padding: var(--input-padding-vertical) var(--input-padding-horizontal);
    text-align: center;
    outline: 1px solid transparent;
    outline-offset: 2px !important;
    color: var(--vscode-button-secondaryForeground) !important;
    background: var(--vscode-button-secondaryBackground) !important;
}

button:hover {
    background: var(--vscode-button-secondaryHoverBackground) !important;
}

button:hover svg {
    stroke: var(--vscode-button-secondaryForeground) !important;
}

button:focus {
    outline-color: var(--vscode-focusBorder);
}

button.secondary {
    color: var(--vscode-button-secondaryForeground);
    background: var(--vscode-button-secondaryBackground);
}

button.secondary:hover {
    background: var(--vscode-button-secondaryHoverBackground);
}

input:not([type='checkbox']),
textarea {
    display: block;
    width: 100%;
    border: none;
    font-family: var(--vscode-font-family);
    padding: var(--input-padding-vertical) var(--input-padding-horizontal);
    color: var(--vscode-input-foreground);
    outline-color: var(--vscode-input-border);
    background-color: var(--vscode-input-background);
}

input::placeholder,
textarea::placeholder {
    color: var(--vscode-input-placeholderForeground);
}

[contenteditable='true'] {
    outline: 1px solid var(--vscode-focusBorder);
}

/* CSS Spinner */
.spinner {
    width: 36px;
    text-align: center;
}

.spinner>div {
    width: 4px;
    height: 4px;
    background-color: #888;

    border-radius: 100%;
    display: inline-block;
    -webkit-animation: sk-bouncedelay 1.4s infinite ease-in-out both;
    animation: sk-bouncedelay 1.4s infinite ease-in-out both;
}

.spinner .bounce1 {
    -webkit-animation-delay: -0.32s;
    animation-delay: -0.32s;
}

.spinner .bounce2 {
    -webkit-animation-delay: -0.16s;
    animation-delay: -0.16s;
}

@-webkit-keyframes sk-bouncedelay {

    0%,
    80%,
    100% {
        -webkit-transform: scale(0)
    }

    40% {
        -webkit-transform: scale(1.0)
    }
}

@keyframes sk-bouncedelay {

    0%,
    80%,
    100% {
        -webkit-transform: scale(0);
        transform: scale(0);
    }

    40% {
        -webkit-transform: scale(1.0);
        transform: scale(1.0);
    }
}

.textarea-wrapper {
    display: grid;
    max-height: 20rem;
    font-size: var(--vscode-font-size);
}

.textarea-wrapper::after {
    content: attr(data-replicated-value) " ";
    white-space: pre-wrap;
    visibility: hidden;
}

.textarea-wrapper>textarea {
    resize: none;
    overflow: hidden auto;
    max-height: 20rem;
}

.textarea-wrapper>textarea,
.textarea-wrapper::after {
    border: 1px solid black;
    padding: .5rem 5rem .5rem .5rem;
    font: inherit;
    grid-area: 1 / 1 / 2 / 2;
}

.pre-code-element:not(:last-child) {
    margin-bottom: 2rem;
}

.code-actions-wrapper {
    opacity: 0.70;
    font-size: 12px;
    margin-top: 1rem;
}

.code-actions-wrapper:hover {
    opacity: 1;
    display: flex;
}

.typing {
    font-size: var(--vscode-font-size);
}

.input-background {
    background: var(--vscode-input-background);
}

.send-element-ext,
.cancel-element-ext {
    font-size: smaller;
}

@-webkit-keyframes blink {
    to {
        visibility: hidden
    }
}

@keyframes blink {
    to {
        visibility: hidden
    }
}

.result-streaming>:not(ol):not(ul):not(pre):last-child:after,
.result-streaming>ol:last-child li:last-child:after,
.result-streaming>pre:last-child code:after,
.result-streaming>ul:last-child li:last-child:after {
    -webkit-animation: blink 1s steps(5, start) infinite;
    animation: blink 1s steps(5, start) infinite;
    content: "▋";
    margin-left: 0.25rem;
    vertical-align: baseline;
}

@media (max-height: 560px) {
    .features-block {
        display: none !important;
    }
}

.hidden {
    display: hidden;
}

.answer-element-ext table {
    --tw-border-spacing-x: 0px;
    --tw-border-spacing-y: 0px;
    border-collapse: separate;
    border-spacing: var(--tw-border-spacing-x) var(--tw-border-spacing-y);
    width: 100%;
    text-align: left;
}

.answer-element-ext th {
    background-color: var(--vscode-input-background);
    border-bottom-width: 1px;
    border-left-width: 1px;
    border-top-width: 1px;
    padding: .25rem .75rem;
}

.answer-element-ext th:first-child {
    border-top-left-radius: .375rem;
}

.answer-element-ext th:last-child {
    border-right-width: 1px;
    border-top-right-radius: .375rem;
}

.answer-element-ext td {
    border-bottom-width: 1px;
    border-left-width: 1px;
    padding: .25rem .75rem;
}

.answer-element-ext td:last-child {
    border-right-width: 1px
}

.answer-element-ext tbody tr:last-child td:first-child {
    border-bottom-left-radius: .375rem;
}

.answer-element-ext tbody tr:last-child td:last-child {
    border-bottom-right-radius: .375rem;
}

.answer-element-ext a {
    text-decoration-line: underline;
    text-underline-offset: 2px;
}

/* Augment-style Clean UI Components */
.uiorbit-container {
    display: flex;
    flex-direction: column;
    height: 100vh;
    background-color: var(--vscode-editor-background);
}

.uiorbit-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: var(--spacing-md);
    border-bottom: 1px solid var(--vscode-panel-border);
    background-color: var(--vscode-sideBar-background);
}

.uiorbit-logo {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    font-weight: 600;
    font-size: 16px;
    color: var(--vscode-foreground);
}

.uiorbit-logo svg {
    width: 24px;
    height: 24px;
}

.mode-toggle {
    display: flex;
    background-color: var(--vscode-input-background);
    border-radius: var(--border-radius);
    padding: 2px;
    border: 1px solid var(--vscode-input-border);
}

.mode-toggle button {
    padding: var(--spacing-xs) var(--spacing-sm);
    border: none;
    background: transparent;
    color: var(--vscode-foreground);
    border-radius: calc(var(--border-radius) - 2px);
    font-size: 12px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
}

.mode-toggle button.active {
    background-color: var(--vscode-button-background);
    color: var(--vscode-button-foreground);
}

.mode-toggle button:hover:not(.active) {
    background-color: var(--vscode-button-secondaryHoverBackground);
}

.chat-container {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

.messages-area {
    flex: 1;
    overflow-y: auto;
    padding: var(--spacing-md);
    scroll-behavior: smooth;
}

.message {
    margin-bottom: var(--spacing-lg);
    display: flex;
    flex-direction: column;
}

.message.user {
    align-items: flex-end;
}

.message.assistant {
    align-items: flex-start;
}

.message-content {
    max-width: 85%;
    padding: var(--spacing-md);
    border-radius: var(--border-radius-lg);
    position: relative;
}

.message.user .message-content {
    background-color: var(--vscode-button-background);
    color: var(--vscode-button-foreground);
    border-bottom-right-radius: var(--spacing-xs);
}

.message.assistant .message-content {
    background-color: var(--vscode-input-background);
    color: var(--vscode-foreground);
    border: 1px solid var(--vscode-input-border);
    border-bottom-left-radius: var(--spacing-xs);
}

.message-avatar {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: var(--spacing-xs);
    font-size: 14px;
    font-weight: 600;
}

.message.user .message-avatar {
    background-color: var(--vscode-button-background);
    color: var(--vscode-button-foreground);
}

.message.assistant .message-avatar {
    background-color: var(--vscode-input-background);
    color: var(--vscode-foreground);
    border: 1px solid var(--vscode-input-border);
}

.input-area {
    padding: var(--spacing-md);
    border-top: 1px solid var(--vscode-panel-border);
    background-color: var(--vscode-sideBar-background);
}

.input-container {
    position: relative;
    display: flex;
    align-items: flex-end;
    gap: var(--spacing-sm);
    background-color: var(--vscode-input-background);
    border: 1px solid var(--vscode-input-border);
    border-radius: var(--border-radius-lg);
    padding: var(--spacing-sm);
    transition: border-color 0.2s ease;
}

.input-container:focus-within {
    border-color: var(--vscode-focusBorder);
}

.input-field {
    flex: 1;
    border: none;
    background: transparent;
    color: var(--vscode-foreground);
    font-family: var(--vscode-font-family);
    font-size: var(--vscode-editor-font-size);
    resize: none;
    outline: none;
    min-height: 20px;
    max-height: 120px;
    line-height: 1.4;
    padding: 0;
}

.input-field::placeholder {
    color: var(--vscode-input-placeholderForeground);
}

.input-actions {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
}

.action-button {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 32px;
    height: 32px;
    border: none;
    background: transparent;
    color: var(--vscode-foreground);
    border-radius: var(--border-radius);
    cursor: pointer;
    transition: all 0.2s ease;
}

.action-button:hover {
    background-color: var(--vscode-button-secondaryHoverBackground);
}

.action-button.primary {
    background-color: var(--vscode-button-background);
    color: var(--vscode-button-foreground);
}

.action-button.primary:hover {
    background-color: var(--vscode-button-hoverBackground);
}

.action-button svg {
    width: 16px;
    height: 16px;
}

/* File Attachments */
.file-attachments {
    display: flex;
    flex-wrap: wrap;
    gap: var(--spacing-xs);
    margin-bottom: var(--spacing-sm);
}

.file-attachment {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    padding: var(--spacing-xs) var(--spacing-sm);
    background-color: var(--vscode-button-secondaryBackground);
    color: var(--vscode-button-secondaryForeground);
    border-radius: var(--border-radius);
    font-size: 12px;
    border: 1px solid var(--vscode-button-border);
}

.file-attachment .remove-file {
    background: none;
    border: none;
    color: inherit;
    cursor: pointer;
    padding: 0;
    width: 14px;
    height: 14px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.file-attachment .remove-file:hover {
    opacity: 0.7;
}

.file-drop-zone {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 122, 255, 0.1);
    border: 2px dashed var(--vscode-focusBorder);
    border-radius: var(--border-radius-lg);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 14px;
    color: var(--vscode-focusBorder);
    opacity: 0;
    pointer-events: none;
    transition: opacity 0.2s ease;
}

.file-drop-zone.active {
    opacity: 1;
}

/* @ Mentions */
.mention-dropdown {
    position: absolute;
    bottom: 100%;
    left: 0;
    right: 0;
    background-color: var(--vscode-dropdown-background);
    border: 1px solid var(--vscode-dropdown-border);
    border-radius: var(--border-radius);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    max-height: 200px;
    overflow-y: auto;
    z-index: 1000;
    margin-bottom: var(--spacing-xs);
}

.mention-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-sm);
    cursor: pointer;
    border-bottom: 1px solid var(--vscode-dropdown-border);
}

.mention-item:last-child {
    border-bottom: none;
}

.mention-item:hover,
.mention-item.selected {
    background-color: var(--vscode-list-hoverBackground);
}

.mention-item .icon {
    width: 16px;
    height: 16px;
    color: var(--vscode-symbolIcon-fileForeground);
}

.mention-item .name {
    font-weight: 500;
    color: var(--vscode-foreground);
}

.mention-item .path {
    font-size: 12px;
    color: var(--vscode-descriptionForeground);
}

.mention-tag {
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-xs);
    padding: 2px var(--spacing-xs);
    background-color: var(--vscode-button-secondaryBackground);
    color: var(--vscode-button-secondaryForeground);
    border-radius: var(--spacing-xs);
    font-size: 12px;
    font-weight: 500;
}

/* Codebase Summary */
.codebase-summary {
    padding: var(--spacing-md);
    background-color: var(--vscode-input-background);
    border: 1px solid var(--vscode-input-border);
    border-radius: var(--border-radius);
    margin-bottom: var(--spacing-md);
}

.codebase-summary h3 {
    margin: 0 0 var(--spacing-sm) 0;
    font-size: 14px;
    font-weight: 600;
    color: var(--vscode-foreground);
}

.codebase-summary p {
    margin: 0;
    font-size: 12px;
    color: var(--vscode-descriptionForeground);
    line-height: 1.4;
}

/* Loading States */
.loading-indicator {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-md);
    color: var(--vscode-descriptionForeground);
    font-size: 14px;
}

.loading-dots {
    display: flex;
    gap: 2px;
}

.loading-dots span {
    width: 4px;
    height: 4px;
    background-color: currentColor;
    border-radius: 50%;
    animation: loading-pulse 1.4s infinite ease-in-out;
}

.loading-dots span:nth-child(1) { animation-delay: -0.32s; }
.loading-dots span:nth-child(2) { animation-delay: -0.16s; }
.loading-dots span:nth-child(3) { animation-delay: 0s; }

@keyframes loading-pulse {
    0%, 80%, 100% {
        transform: scale(0);
        opacity: 0.5;
    }
    40% {
        transform: scale(1);
        opacity: 1;
    }
}

/* Responsive Design */
@media (max-width: 480px) {
    .uiorbit-header {
        padding: var(--spacing-sm);
    }

    .messages-area {
        padding: var(--spacing-sm);
    }

    .input-area {
        padding: var(--spacing-sm);
    }

    .message-content {
        max-width: 95%;
    }
}