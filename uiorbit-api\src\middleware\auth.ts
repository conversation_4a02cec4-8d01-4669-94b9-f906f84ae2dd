import { Request, Response, NextFunction } from 'express'
import { createClient } from '@supabase/supabase-js'
import { logger } from '../utils/logger'

const supabase = createClient(
  process.env.SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
)

export interface AuthenticatedRequest extends Request {
  user?: {
    id: string
    email: string
    tier: 'free' | 'pro' | 'enterprise'
    usage: {
      monthlyRequests: number
      usedRequests: number
    }
  }
}

export async function authMiddleware(
  req: AuthenticatedRequest,
  res: Response,
  next: NextFunction
) {
  try {
    const authHeader = req.headers.authorization
    
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return res.status(401).json({ error: 'Missing or invalid authorization header' })
    }

    const token = authHeader.substring(7)

    // Verify the JWT token with Supabase
    const { data: { user }, error } = await supabase.auth.getUser(token)

    if (error || !user) {
      logger.warn('Invalid token provided', { error })
      return res.status(401).json({ error: 'Invalid or expired token' })
    }

    // Get user profile and usage data
    const { data: profile, error: profileError } = await supabase
      .from('user_profiles')
      .select('*')
      .eq('id', user.id)
      .single()

    if (profileError) {
      logger.error('Failed to fetch user profile', { error: profileError, userId: user.id })
      return res.status(500).json({ error: 'Failed to fetch user data' })
    }

    // Attach user data to request
    req.user = {
      id: user.id,
      email: user.email!,
      tier: profile.tier || 'free',
      usage: {
        monthlyRequests: profile.monthly_requests || 0,
        usedRequests: profile.used_requests || 0
      }
    }

    next()

  } catch (error) {
    logger.error('Auth middleware error', { error })
    res.status(500).json({ error: 'Authentication failed' })
  }
}
