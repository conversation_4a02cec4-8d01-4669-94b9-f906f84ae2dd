import delay from 'delay';

import fetch from 'isomorphic-fetch';

import * as fs from 'node:fs';
import * as os from 'node:os';
import * as path from 'node:path';

import * as vscode from 'vscode';

import { ChatGPTAPI as ChatGPTAPI3 } from '../chatgpt-4.7.2/index';
import { ChatGPTAPI as ChatGPTAPI35 } from '../chatgpt-5.1.1/index';

import { AdvancedAIEngine } from "./advanced-ai-engine";
import { ASTAnalyzer } from "./ast-analyzer";
import { CollaborationEngine } from "./collaboration-engine";
import { ComponentLibraryManager } from "./component-library";
import { EnterpriseManager } from "./enterprise-features";
import { EnhancedFileManager } from "./file-manager";
import { FileWatcher } from "./file-watcher";
import { LivePreviewManager } from "./live-preview";
import { ProjectScaffolder } from "./project-scaffolder";
import { AuthType, LoginMethod } from "./types";
import { UIPromptTemplates, UIPromptContext } from "./ui-prompts";
import { VectorDatabase } from "./vector-database";

export default class UIOrbitViewProvider implements vscode.WebviewViewProvider {
	private webView?: vscode.WebviewView;

	public subscribeToResponse: boolean;
	public autoScroll: boolean;
	public useGpt3?: boolean;
	public model?: string;
	public framework?: string;
	public stylingFramework?: string;
	public designSystem?: string;
	public accessibilityLevel?: string;

	// UIOrbit-specific services
	private vectorDb: VectorDatabase;
	private astAnalyzer: ASTAnalyzer;
	private fileWatcher: FileWatcher;
	private projectScaffolder: ProjectScaffolder;
	private livePreview: LivePreviewManager;
	private componentLibrary: ComponentLibraryManager;
	private fileManager: EnhancedFileManager;
	private aiEngine: AdvancedAIEngine;
	private collaborationEngine: CollaborationEngine;
	private enterpriseManager: EnterpriseManager;

	private apiGpt3?: ChatGPTAPI3;
	private apiGpt35?: ChatGPTAPI35;
	private conversationId?: string;
	private messageId?: string;
	private proxyServer?: string;
	private loginMethod?: LoginMethod;
	private authType?: AuthType;

	private questionCounter: number = 0;
	private inProgress: boolean = false;
	private abortController?: AbortController;
	private currentMessageId: string = "";
	private response: string = "";

	/**
	 * Message to be rendered lazily if they haven't been rendered
	 * in time before resolveWebviewView is called.
	 */
	private leftOverMessage?: any;
	constructor(private context: vscode.ExtensionContext) {
		this.subscribeToResponse = vscode.workspace.getConfiguration("uiorbit").get("response.showNotification") || false;
		this.autoScroll = !!vscode.workspace.getConfiguration("uiorbit").get("response.autoScroll");
		this.model = vscode.workspace.getConfiguration("uiorbit").get("model") as string;
		this.framework = vscode.workspace.getConfiguration("uiorbit").get("framework") as string;
		this.stylingFramework = vscode.workspace.getConfiguration("uiorbit").get("stylingFramework") as string;
		this.designSystem = vscode.workspace.getConfiguration("uiorbit").get("designSystem") as string;
		this.accessibilityLevel = vscode.workspace.getConfiguration("uiorbit").get("accessibilityLevel") as string;

		// Initialize UIOrbit services
		this.vectorDb = new VectorDatabase(context);
		this.astAnalyzer = new ASTAnalyzer();
		this.fileWatcher = new FileWatcher(context, this.vectorDb);

		// Initialize enterprise features
		const workspaceRoot = vscode.workspace.workspaceFolders?.[0]?.uri.fsPath || '';
		this.projectScaffolder = new ProjectScaffolder(workspaceRoot);
		this.livePreview = new LivePreviewManager(workspaceRoot);
		this.componentLibrary = new ComponentLibraryManager(workspaceRoot);
		this.fileManager = new EnhancedFileManager(workspaceRoot);
		this.aiEngine = new AdvancedAIEngine(workspaceRoot);
		this.collaborationEngine = new CollaborationEngine(workspaceRoot);
		this.enterpriseManager = new EnterpriseManager(workspaceRoot);

		// Initialize enterprise features
		this.enterpriseManager.initializeEnterprise();

		this.setMethod();
	}

	public resolveWebviewView(
		webviewView: vscode.WebviewView,
		_context: vscode.WebviewViewResolveContext,
		_token: vscode.CancellationToken,
	) {
		this.webView = webviewView;

		webviewView.webview.options = {
			// Allow scripts in the webview
			enableScripts: true,

			localResourceRoots: [
				this.context.extensionUri
			]
		};

		webviewView.webview.html = this.getWebviewHtml(webviewView.webview);

		webviewView.webview.onDidReceiveMessage(async data => {
			switch (data.type) {
				case 'sendMessage':
					this.handleNewMessage(data.message, data.files, data.mode);
					break;
				case 'indexCodebase':
					this.indexCodebase();
					break;
				case 'getMentionSuggestions':
					this.getMentionSuggestions(data.query);
					break;
				case 'addFreeTextQuestion':
					this.sendApiRequest(data.value, { command: "freeText" });
					break;
				case 'editCode':
					const escapedString = (data.value as string).replace(/\$/g, '\\$');;
					vscode.window.activeTextEditor?.insertSnippet(new vscode.SnippetString(escapedString));

					this.logEvent("code-inserted");
					break;
				case 'openNew':
					const document = await vscode.workspace.openTextDocument({
						content: data.value,
						language: data.language
					});
					vscode.window.showTextDocument(document);

					this.logEvent(data.language === "markdown" ? "code-exported" : "code-opened");
					break;
				case 'clearConversation':
					this.messageId = undefined;
					this.conversationId = undefined;

					this.logEvent("conversation-cleared");
					break;
				case 'clearBrowser':
					this.logEvent("browser-cleared");
					break;
				case 'cleargpt3':
					this.apiGpt3 = undefined;

					this.logEvent("gpt3-cleared");
					break;
				case 'login':
					this.prepareConversation().then(success => {
						if (success) {
							this.sendMessage({ type: 'loginSuccessful', showConversations: false }, true);

							this.logEvent("logged-in");
						}
					});
					break;
				case 'openSettings':
					vscode.commands.executeCommand('workbench.action.openSettings', "@ext:UIOrbit.uiorbit-vscode uiorbit.");

					this.logEvent("settings-opened");
					break;
				case 'openSettingsPrompt':
					vscode.commands.executeCommand('workbench.action.openSettings', "@ext:UIOrbit.uiorbit-vscode promptPrefix");

					this.logEvent("settings-prompt-opened");
					break;
				case 'listConversations':
					this.logEvent("conversations-list-attempted");
					break;
				case 'showConversation':
					/// ...
					break;
				case "stopGenerating":
					this.stopGenerating();
					break;
				default:
					break;
			}
		});

		if (this.leftOverMessage !== null) {
			// If there were any messages that wasn't delivered, render after resolveWebView is called.
			this.sendMessage(this.leftOverMessage);
			this.leftOverMessage = null;
		}
	}

	private stopGenerating(): void {
		this.abortController?.abort?.();
		this.inProgress = false;
		this.sendMessage({ type: 'showInProgress', inProgress: this.inProgress });
		const responseInMarkdown = !this.isCodexModel;
		this.sendMessage({ type: 'addResponse', value: this.response, done: true, id: this.currentMessageId, autoScroll: this.autoScroll, responseInMarkdown });
		this.logEvent("stopped-generating");
	}

	public clearSession(): void {
		this.stopGenerating();
		this.apiGpt3 = undefined;
		this.messageId = undefined;
		this.conversationId = undefined;
		this.logEvent("cleared-session");
	}

	public setMethod(): void {
		this.loginMethod = vscode.workspace.getConfiguration("uiorbit").get("method") as LoginMethod;
		this.useGpt3 = true;
		this.clearSession();
	}

	public setUIFramework(): void {
		this.framework = vscode.workspace.getConfiguration("uiorbit").get("framework");
		this.stylingFramework = vscode.workspace.getConfiguration("uiorbit").get("stylingFramework");
		this.designSystem = vscode.workspace.getConfiguration("uiorbit").get("designSystem");
		this.accessibilityLevel = vscode.workspace.getConfiguration("uiorbit").get("accessibilityLevel");
	}

	private get isCodexModel(): boolean {
		return !!this.model?.startsWith("code-");
	}

	private get isGpt35Model(): boolean {
		return !!this.model?.startsWith("gpt-");
	}

	public async prepareConversation(modelChanged = false): Promise<boolean> {
		if (modelChanged) {
			// Model changed, need to reinitialize
			// Continue with reinitialization
		}

		const state = this.context.globalState;
		const configuration = vscode.workspace.getConfiguration("uiorbit");

		if (this.useGpt3) {
			if ((this.isGpt35Model && !this.apiGpt35) || (!this.isGpt35Model && !this.apiGpt3) || modelChanged) {
				let apiKey = configuration.get("apiKey") as string || state.get("uiorbit-api-key") as string;
				const organization = configuration.get("organization") as string;
				const maxTokens = configuration.get("maxTokens") as number;
				const temperature = configuration.get("temperature") as number;
				const apiBaseUrl = configuration.get("apiBaseUrl") as string;

				if (!apiKey) {
					vscode.window.showErrorMessage("Please add your OpenAI API Key to use UIOrbit. You can store it in settings or enter it temporarily for this session.", "Store in session (Recommended)", "Open settings").then(async choice => {
						if (choice === "Open settings") {
							vscode.commands.executeCommand('workbench.action.openSettings', "uiorbit.apiKey");
							return false;
						} else if (choice === "Store in session (Recommended)") {
							await vscode.window
								.showInputBox({
									title: "Store OpenAI API Key in session",
									prompt: "Please enter your OpenAI API Key to store in your session only. This option won't persist the token on your settings.json file. You may need to re-enter after restarting your VS-Code",
									ignoreFocusOut: true,
									placeHolder: "API Key",
									value: apiKey || ""
								})
								.then((value) => {
									if (value) {
										apiKey = value;
										state.update("uiorbit-api-key", apiKey);
										this.sendMessage({ type: 'loginSuccessful', showConversations: false }, true);
									}
								});
						}
					});

					return false;
				}

				if (this.isGpt35Model) {
					this.apiGpt35 = new ChatGPTAPI35({
						apiKey,
						fetch: fetch,
						apiBaseUrl: apiBaseUrl?.trim() || undefined,
						organization,
						completionParams: {
							model: this.model,
							max_tokens: maxTokens,
							temperature,
						}
					});
				} else {
					this.apiGpt3 = new ChatGPTAPI3({
						apiKey,
						fetch: fetch,
						apiBaseUrl: apiBaseUrl?.trim() || undefined,
						organization,
						completionParams: {
							model: this.model,
							max_tokens: maxTokens,
							temperature,
						}
					});
				}
			}
		}

		this.sendMessage({ type: 'loginSuccessful', showConversations: false }, true);

		return true;
	}

	private get systemContext() {
		return `You are ChatGPT helping the User with coding. 
			You are intelligent, helpful and an expert developer, who always gives the correct answer and only does what instructed. You always answer truthfully and don't make things up. 
			(When responding to the following prompt, please make sure to properly style your response using Github Flavored Markdown. 
			Use markdown syntax for things like headings, lists, colored text, code blocks, highlights etc. Make sure not to mention markdown or styling in your actual response.)`;
	}

	private processQuestion(question: string, code?: string, language?: string) {
		// Add UI/UX context to the prompt
		let uiContext = this.buildUIContext();

		if (code !== null) {
			// Add prompt prefix to the code if there was a code block selected
			question = `${uiContext}\n\n${question}${language ? ` (The following code is in ${language} programming language)` : ''}: ${code}`;
		} else {
			question = `${uiContext}\n\n${question}`;
		}
		return question + "\r\n";
	}

	private buildUIContext(): string {
		const framework = this.framework || 'Auto-detect';
		const styling = this.stylingFramework || 'Auto-detect';
		const designSystem = this.designSystem || 'None';
		const accessibility = this.accessibilityLevel || 'WCAG AA';

		return `You are UIOrbit, an expert UI/UX developer assistant specializing in modern frontend development.

Current Project Context:
- Framework: ${framework}
- Styling: ${styling}
- Design System: ${designSystem}
- Accessibility Level: ${accessibility}

Guidelines:
- Generate modern, responsive, and accessible UI components
- Follow current UI/UX best practices and trends
- Use semantic HTML and proper ARIA attributes
- Ensure mobile-first responsive design
- Apply the specified design system principles when applicable
- Write clean, maintainable code with proper component structure
- Include helpful comments explaining design decisions`;
	}

	private async processUIQuestion(prompt: string, options: { command: string, code?: string, language?: string }): Promise<string> {
		const context: UIPromptContext = {
			framework: this.framework,
			stylingFramework: this.stylingFramework,
			designSystem: this.designSystem,
			accessibilityLevel: this.accessibilityLevel,
			language: options.language
		};

		// Get project context for enhanced suggestions
		const projectContext = await this.getEnhancedProjectContext();
		let enhancedPrompt = prompt;

		// Use specific UI prompts based on command with AI enhancement
		switch (options.command) {
			case 'generateComponent':
				const codeContext = await this.aiEngine.buildCodeContext();
				enhancedPrompt = await this.aiEngine.generateContextAwareComponent(prompt, codeContext);
				break;
			case 'addStyling':
				enhancedPrompt = UIPromptTemplates.addStyling(context);
				break;
			case 'makeResponsive':
				enhancedPrompt = UIPromptTemplates.makeResponsive(context);
				break;
			case 'addAccessibility':
				enhancedPrompt = UIPromptTemplates.addAccessibility(context);
				break;
			case 'optimizeUI':
				enhancedPrompt = UIPromptTemplates.optimizeUI(context);
				break;
			case 'explainDesign':
				enhancedPrompt = UIPromptTemplates.explainDesign(context);
				break;
			case 'generateVariants':
				enhancedPrompt = UIPromptTemplates.generateVariants(context);
				break;
			case 'trendingPatterns':
				enhancedPrompt = UIPromptTemplates.trendingPatterns(context);
				break;
			case 'designSystem':
				enhancedPrompt = UIPromptTemplates.designSystem(context);
				break;
			case 'customUIPrompt':
				enhancedPrompt = UIPromptTemplates.customUIPrompt(context, prompt);
				break;
			default:
				// For freeText and other commands, use the original prompt with UI context
				enhancedPrompt = this.buildUIContext() + '\n\n' + prompt;
				break;
		}

		// Add project context for better suggestions
		if (projectContext) {
			enhancedPrompt += `\n\nProject Context:\n${projectContext}`;
		}

		if (options.code) {
			enhancedPrompt += `${options.language ? ` (The following code is in ${options.language} programming language)` : ''}: ${options.code}`;
		}

		return enhancedPrompt + "\r\n";
	}

	private async getEnhancedProjectContext(): Promise<string> {
		try {
			const fileContext = await this.fileWatcher.getContextForCurrentFile();
			const projectSummary = await this.fileWatcher.getProjectSummary();

			let contextInfo = `Project Summary:
- Total Files: ${projectSummary.totalFiles}
- Components: ${projectSummary.components}
- Frameworks: ${projectSummary.frameworks.join(', ')}
- Recent Changes: ${projectSummary.recentChanges}`;

			if (fileContext.currentFile) {
				contextInfo += `\n\nCurrent File Context:
- Framework: ${fileContext.currentFile.framework}
- Dependencies: ${fileContext.currentFile.dependencies.join(', ')}
- Components: ${fileContext.currentFile.components.join(', ')}`;
			}

			if (fileContext.suggestions.length > 0) {
				contextInfo += `\n\nSuggestions:
${fileContext.suggestions.map(s => `- ${s}`).join('\n')}`;
			}

			return contextInfo;
		} catch (error) {
			console.error('Error getting project context:', error);
			return '';
		}
	}

	public async sendApiRequest(prompt: string, options: { command: string, code?: string, previousAnswer?: string, language?: string; }) {
		if (this.inProgress) {
			// The AI is still thinking... Do not accept more questions.
			return;
		}

		this.questionCounter++;

		this.logEvent("api-request-sent", { "uiorbit.command": options.command, "uiorbit.hasCode": String(!!options.code), "uiorbit.hasPreviousAnswer": String(!!options.previousAnswer) });

		// Check if we need to scaffold a new project
		if (await this.projectScaffolder.needsScaffolding() && this.isComponentGenerationRequest(prompt)) {
			const shouldScaffold = await vscode.window.showInformationMessage(
				'🚀 No project detected. Would you like UIOrbit to create a new project for you?',
				'Yes, create project',
				'No, continue anyway'
			);

			if (shouldScaffold === 'Yes, create project') {
				const recommendations = this.projectScaffolder.getProjectRecommendations(prompt);
				this.sendMessage({
					type: 'addResponse',
					value: recommendations,
					autoScroll: this.autoScroll
				});

				const shouldProceed = await vscode.window.showInformationMessage(
					'Create this project setup?',
					'Yes, create it',
					'No, let me choose'
				);

				if (shouldProceed === 'Yes, create it') {
					const config = this.projectScaffolder.detectFrameworkFromPrompt(prompt);
					const success = await this.projectScaffolder.createProject(config);
					if (success) {
						return; // Project created, user will be redirected
					}
				}
			}
		}

		if (!await this.prepareConversation()) {
			return;
		}

		this.response = '';
		let question = await this.processUIQuestion(prompt, options);
		const responseInMarkdown = !this.isCodexModel;

		// If the UIOrbit view is not in focus/visible; focus on it to render Q&A
		if (this.webView == null) {
			vscode.commands.executeCommand('uiorbit.view.focus');
		} else {
			this.webView?.show?.(true);
		}

		this.inProgress = true;
		this.abortController = new AbortController();
		this.sendMessage({ type: 'showInProgress', inProgress: this.inProgress, showStopButton: this.useGpt3 });
		this.currentMessageId = this.getRandomId();

		this.sendMessage({ type: 'addQuestion', value: prompt, code: options.code, autoScroll: this.autoScroll });

		try {
			if (this.useGpt3) {
				if (this.isGpt35Model && this.apiGpt35) {
					const gpt3Response = await this.apiGpt35.sendMessage(question, {
						systemMessage: this.systemContext,
						messageId: this.conversationId,
						parentMessageId: this.messageId,
						abortSignal: this.abortController.signal,
						onProgress: (partialResponse) => {
							this.response = partialResponse.text;
							this.sendMessage({ type: 'addResponse', value: this.response, id: this.currentMessageId, autoScroll: this.autoScroll, responseInMarkdown });
						},
					});
					({ text: this.response, id: this.conversationId, parentMessageId: this.messageId } = gpt3Response);
				} else if (!this.isGpt35Model && this.apiGpt3) {
					({ text: this.response, conversationId: this.conversationId, parentMessageId: this.messageId } = await this.apiGpt3.sendMessage(question, {
						promptPrefix: this.systemContext,
						abortSignal: this.abortController.signal,
						onProgress: (partialResponse) => {
							this.response = partialResponse.text;
							this.sendMessage({ type: 'addResponse', value: this.response, id: this.currentMessageId, autoScroll: this.autoScroll, responseInMarkdown });
						},
					}));
				}
			}

			if (options.previousAnswer !== null) {
				this.response = options.previousAnswer + this.response;
			}

			const hasContinuation = ((this.response.split("```").length) % 2) === 0;

			if (hasContinuation) {
				this.response = this.response + " \r\n ```\r\n";
				vscode.window.showInformationMessage("It looks like ChatGPT didn't complete their answer for your coding question. You can ask it to continue and combine the answers.", "Continue and combine answers")
					.then(async (choice) => {
						if (choice === "Continue and combine answers") {
							this.sendApiRequest("Continue", { command: options.command, code: undefined, previousAnswer: this.response });
						}
					});
			}

			this.sendMessage({ type: 'addResponse', value: this.response, done: true, id: this.currentMessageId, autoScroll: this.autoScroll, responseInMarkdown });

			// Handle generated code with live preview
			if (options.command === 'generateComponent' || this.isComponentGenerationRequest(prompt)) {
				await this.handleGeneratedCode(this.response, this.framework || 'react');
			}

			if (this.subscribeToResponse) {
				vscode.window.showInformationMessage("UIOrbit responded to your question.", "Open conversation").then(async () => {
					await vscode.commands.executeCommand('uiorbit.view.focus');
				});
			}
		} catch (error: any) {
			let message;
			let apiMessage = error?.response?.data?.error?.message || error?.tostring?.() || error?.message || error?.name;

			this.logError("api-request-failed");

			if (error?.response?.status || error?.response?.statusText) {
				message = `${error?.response?.status || ""} ${error?.response?.statusText || ""}`;

				vscode.window.showErrorMessage("An error occurred. If this is due to max_token you could try `UIOrbit: Clear Conversation` command and retry sending your prompt.", "Clear conversation and retry").then(async choice => {
					if (choice === "Clear conversation and retry") {
						await vscode.commands.executeCommand("uiorbit.clearConversation");
						await delay(250);
						this.sendApiRequest(prompt, { command: options.command, code: options.code });
					}
				});
			} else if (error.statusCode === 400) {
				message = `Your method: '${this.loginMethod}' and your model: '${this.model}' may be incompatible or one of your parameters is unknown. Reset your settings to default. (HTTP 400 Bad Request)`;

			} else if (error.statusCode === 401) {
				message = 'Make sure you are properly signed in. If you are using Browser Auto-login method, make sure the browser is open (You could refresh the browser tab manually if you face any issues, too). If you stored your API key in settings.json, make sure it is accurate. If you stored API key in session, you can reset it with `ChatGPT: Reset session` command. (HTTP 401 Unauthorized) Potential reasons: \r\n- 1.Invalid Authentication\r\n- 2.Incorrect API key provided.\r\n- 3.Incorrect Organization provided. \r\n See https://platform.openai.com/docs/guides/error-codes for more details.';
			} else if (error.statusCode === 403) {
				message = 'Your token has expired. Please try authenticating again. (HTTP 403 Forbidden)';
			} else if (error.statusCode === 404) {
				message = `Your method: '${this.loginMethod}' and your model: '${this.model}' may be incompatible or you may have exhausted your ChatGPT subscription allowance. (HTTP 404 Not Found)`;
			} else if (error.statusCode === 429) {
				message = "Too many requests try again later. (HTTP 429 Too Many Requests) Potential reasons: \r\n 1. You exceeded your current quota, please check your plan and billing details\r\n 2. You are sending requests too quickly \r\n 3. The engine is currently overloaded, please try again later. \r\n See https://platform.openai.com/docs/guides/error-codes for more details.";
			} else if (error.statusCode === 500) {
				message = "The server had an error while processing your request, please try again. (HTTP 500 Internal Server Error)\r\n See https://platform.openai.com/docs/guides/error-codes for more details.";
			}

			if (apiMessage) {
				message = `${message ? message + " " : ""}

	${apiMessage}
`;
			}

			this.sendMessage({ type: 'addError', value: message, autoScroll: this.autoScroll });

			return;
		} finally {
			this.inProgress = false;
			this.sendMessage({ type: 'showInProgress', inProgress: this.inProgress });
		}
	}

	/**
	 * Check if the prompt is requesting component generation
	 */
	private isComponentGenerationRequest(prompt: string): boolean {
		const componentKeywords = [
			'component', 'create', 'generate', 'build', 'make',
			'button', 'form', 'card', 'modal', 'navbar', 'sidebar',
			'dashboard', 'page', 'layout', 'ui', 'interface'
		];

		const lowerPrompt = prompt.toLowerCase();
		return componentKeywords.some(keyword => lowerPrompt.includes(keyword));
	}

	/**
	 * Extract and save generated component code
	 */
	private async handleGeneratedCode(response: string, framework: string = 'react'): Promise<void> {
		// Extract code blocks from the response
		const codeBlocks = this.extractCodeBlocks(response);

		if (codeBlocks.length > 0) {
			// Show live preview for the first component
			const mainComponent = codeBlocks.find(block =>
				block.language === 'jsx' ||
				block.language === 'tsx' ||
				block.language === 'vue' ||
				block.language === 'javascript' ||
				block.language === 'typescript'
			);

			if (mainComponent) {
				await this.livePreview.showPreview(mainComponent.code, framework);

				// Offer to save the component
				const shouldSave = await vscode.window.showInformationMessage(
					'💾 Save this component to your project?',
					'Save component',
					'Just preview'
				);

				if (shouldSave === 'Save component') {
					await this.saveGeneratedComponent(mainComponent.code, framework);
				}
			}
		}
	}

	/**
	 * Extract code blocks from markdown response
	 */
	private extractCodeBlocks(response: string): Array<{language: string, code: string}> {
		const codeBlockRegex = /```(\w+)?\n([\s\S]*?)```/g;
		const blocks: Array<{language: string, code: string}> = [];
		let match;

		while ((match = codeBlockRegex.exec(response)) !== null) {
			blocks.push({
				language: match[1] || 'javascript',
				code: match[2].trim()
			});
		}

		return blocks;
	}

	/**
	 * Save generated component to the project
	 */
	private async saveGeneratedComponent(code: string, framework: string): Promise<void> {
		const workspaceRoot = vscode.workspace.workspaceFolders?.[0]?.uri.fsPath;
		if (!workspaceRoot) {
			return;
		}

		const componentName = await vscode.window.showInputBox({
			prompt: 'Enter component name',
			value: 'MyComponent'
		});

		if (componentName) {
			const extension = framework === 'vue' ? '.vue' :
							 framework === 'angular' ? '.component.ts' :
							 '.tsx';

			const filePath = path.join(workspaceRoot, 'src', 'components', `${componentName}${extension}`);

			// Create components directory if it doesn't exist
			const componentsDir = path.dirname(filePath);
			if (!fs.existsSync(componentsDir)) {
				fs.mkdirSync(componentsDir, { recursive: true });
			}

			// Write the component file
			fs.writeFileSync(filePath, code);

			// Open the file
			const document = await vscode.workspace.openTextDocument(filePath);
			await vscode.window.showTextDocument(document);

			vscode.window.showInformationMessage(`✅ Component saved as ${componentName}${extension}`);
		}
	}

	/**
	 * Install a component library
	 */
	async installComponentLibrary(library: 'shadcn' | 'mui' | 'antd' | 'chakra'): Promise<void> {
		const config = {
			name: library,
			framework: this.framework || 'react',
			components: []
		};

		const success = await this.componentLibrary.installLibrary(config as any);
		if (success) {
			this.sendMessage({
				type: 'addResponse',
				value: `✅ ${library} has been installed successfully! You can now use ${library} components in your project.`,
				autoScroll: this.autoScroll
			});
		}
	}

	/**
	 * Generate component from template
	 */
	async generateFromTemplate(): Promise<void> {
		const libraries = ['shadcn', 'mui', 'antd', 'chakra'];
		const selectedLibrary = await vscode.window.showQuickPick(libraries, {
			placeHolder: 'Select a component library'
		});

		if (!selectedLibrary) {
			return;
		}

		const templates = this.componentLibrary.getComponentTemplates(selectedLibrary);
		const templateNames = templates.map(t => `${t.name} - ${t.description}`);

		const selectedTemplate = await vscode.window.showQuickPick(templateNames, {
			placeHolder: 'Select a component template'
		});

		if (!selectedTemplate) {
			return;
		}

		const templateIndex = templateNames.indexOf(selectedTemplate);
		const template = templates[templateIndex];

		const componentName = await vscode.window.showInputBox({
			prompt: 'Enter component name',
			value: 'MyComponent'
		});

		if (!componentName) {
			return;
		}

		const code = await this.componentLibrary.generateComponent(template, componentName);

		// Show the generated code in the chat
		this.sendMessage({
			type: 'addResponse',
			value: `🎨 **Generated ${componentName} using ${selectedLibrary}:**\n\n\`\`\`tsx\n${code}\n\`\`\``,
			autoScroll: this.autoScroll
		});

		// Show live preview
		await this.livePreview.showPreview(code, this.framework || 'react');

		// Offer to save
		const shouldSave = await vscode.window.showInformationMessage(
			'💾 Save this component to your project?',
			'Save component',
			'Just preview'
		);

		if (shouldSave === 'Save component') {
			await this.saveGeneratedComponent(code, this.framework || 'react');
		}
	}

	/**
	 * Start collaboration session
	 */
	async startCollaboration(): Promise<void> {
		const sessionName = await vscode.window.showInputBox({
			prompt: 'Enter collaboration session name',
			value: 'UIOrbit Session'
		});

		if (sessionName) {
			await this.collaborationEngine.startSession(sessionName);
		}
	}

	/**
	 * Show collaboration panel
	 */
	showCollaborationPanel(): void {
		this.collaborationEngine.showCollaborationPanel();
	}

	/**
	 * Show enterprise dashboard
	 */
	showEnterpriseDashboard(): void {
		this.enterpriseManager.showEnterpriseDashboard();
	}

	/**
	 * Check if feature is available for current tier
	 */
	isFeatureAvailable(featureName: string): boolean {
		return this.enterpriseManager.isFeatureAvailable(featureName);
	}

	/**
	 * Track API usage for billing
	 */
	trackAPIUsage(endpoint: string, success: boolean, responseTime: number): void {
		this.enterpriseManager.trackAPIUsage(endpoint, success, responseTime);
	}

	/**
	 * Message sender, stores if a message cannot be delivered
	 * @param message Message to be sent to WebView
	 * @param ignoreMessageIfNullWebView We will ignore the command if webView is null/not-focused
	 */
	public sendMessage(message: any, ignoreMessageIfNullWebView?: boolean) {
		if (this.webView) {
			this.webView?.webview.postMessage(message);
		} else if (!ignoreMessageIfNullWebView) {
			this.leftOverMessage = message;
		}
	}

	private logEvent(eventName: string, properties?: {}): void {
		// You can initialize your telemetry reporter and consume it here - *replaced with console.debug to prevent unwanted telemetry logs
		// this.reporter?.sendTelemetryEvent(eventName, { "uiorbit.loginMethod": this.loginMethod!, "uiorbit.framework": this.framework!, "uiorbit.model": this.model || "unknown", ...properties }, { "uiorbit.questionCounter": this.questionCounter });
		console.debug(eventName, { "uiorbit.loginMethod": this.loginMethod!, "uiorbit.framework": this.framework!, "uiorbit.model": this.model || "unknown", ...properties }, { "uiorbit.questionCounter": this.questionCounter });
	}

	private logError(eventName: string): void {
		// You can initialize your telemetry reporter and consume it here - *replaced with console.error to prevent unwanted telemetry logs
		// this.reporter?.sendTelemetryErrorEvent(eventName, { "uiorbit.loginMethod": this.loginMethod!, "uiorbit.framework": this.framework!, "uiorbit.model": this.model || "unknown" }, { "uiorbit.questionCounter": this.questionCounter });
		console.error(eventName, { "uiorbit.loginMethod": this.loginMethod!, "uiorbit.framework": this.framework!, "uiorbit.model": this.model || "unknown" }, { "uiorbit.questionCounter": this.questionCounter });
	}

	private getWebviewHtml(webview: vscode.Webview) {
		const scriptUri = webview.asWebviewUri(vscode.Uri.joinPath(this.context.extensionUri, 'media', 'main.js'));
		const stylesMainUri = webview.asWebviewUri(vscode.Uri.joinPath(this.context.extensionUri, 'media', 'main.css'));

		const vendorHighlightCss = webview.asWebviewUri(vscode.Uri.joinPath(this.context.extensionUri, 'media', 'vendor', 'highlight.min.css'));
		const vendorHighlightJs = webview.asWebviewUri(vscode.Uri.joinPath(this.context.extensionUri, 'media', 'vendor', 'highlight.min.js'));
		const vendorMarkedJs = webview.asWebviewUri(vscode.Uri.joinPath(this.context.extensionUri, 'media', 'vendor', 'marked.min.js'));
		const vendorTailwindJs = webview.asWebviewUri(vscode.Uri.joinPath(this.context.extensionUri, 'media', 'vendor', 'tailwindcss.3.2.4.min.js'));
		const vendorTurndownJs = webview.asWebviewUri(vscode.Uri.joinPath(this.context.extensionUri, 'media', 'vendor', 'turndown.js'));

		const nonce = this.getRandomId();

		return `<!DOCTYPE html>
			<html lang="en">
			<head>
				<meta charset="UTF-8">
				<meta name="viewport" content="width=device-width, initial-scale=1.0">

				<link href="${stylesMainUri}" rel="stylesheet">
				<link href="${vendorHighlightCss}" rel="stylesheet">
				<script src="${vendorHighlightJs}"></script>
				<script src="${vendorMarkedJs}"></script>
				<script src="${vendorTailwindJs}"></script>
				<script src="${vendorTurndownJs}"></script>
			</head>
			<body>
				<div class="uiorbit-container">
					<!-- Header with Logo and Mode Toggle -->
					<div class="uiorbit-header">
						<div class="uiorbit-logo">
							<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor">
								<path d="M12 2L2 7l10 5 10-5-10-5zM2 17l10 5 10-5M2 12l10 5 10-5"/>
							</svg>
							UIOrbit
						</div>
						<div class="mode-toggle">
							<button id="chat-mode" class="active">Chat</button>
							<button id="agent-mode">Agent</button>
						</div>
					</div>

					<!-- Login Screen -->
					<div id="login-screen" class="chat-container" style="display: flex; align-items: center; justify-content: center;">
						<div style="text-align: center; max-width: 400px; padding: 32px;">
							<div style="margin-bottom: 24px;">
								<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" style="width: 48px; height: 48px; margin: 0 auto 16px; color: var(--vscode-button-background);">
									<path d="M12 2L2 7l10 5 10-5-10-5zM2 17l10 5 10-5M2 12l10 5 10-5"/>
								</svg>
								<h2 style="margin: 0 0 8px 0; font-size: 20px; font-weight: 600;">Welcome to UIOrbit</h2>
								<p style="margin: 0; color: var(--vscode-descriptionForeground); font-size: 14px;">AI-powered UI/UX assistant for modern development</p>
							</div>

							<button id="login-button" style="
								width: 100%;
								padding: 12px 24px;
								background-color: var(--vscode-button-background);
								color: var(--vscode-button-foreground);
								border: none;
								border-radius: 8px;
								font-size: 14px;
								font-weight: 500;
								cursor: pointer;
								margin-bottom: 16px;
								transition: background-color 0.2s ease;
							">
								Connect API Key
							</button>

							<div style="display: flex; gap: 16px; justify-content: center; font-size: 12px;">
								<a id="settings-button" href="#" style="color: var(--vscode-textLink-foreground); text-decoration: none;">Settings</a>
								<a id="settings-prompt-button" href="#" style="color: var(--vscode-textLink-foreground); text-decoration: none;">Prompts</a>
							</div>
						</div>
					</div>

					<!-- Main Chat Interface -->
					<div id="main-chat" class="chat-container" style="display: none;">
						<!-- Codebase Summary -->
						<div id="codebase-summary" class="codebase-summary" style="display: none;">
							<h3>📁 Codebase Context</h3>
							<p id="codebase-summary-text">Analyzing your codebase...</p>
						</div>

						<!-- Messages Area -->
						<div class="messages-area" id="messages-container">
							<!-- Messages will be dynamically added here -->
						</div>

						<!-- Loading Indicator -->
						<div id="loading-indicator" class="loading-indicator" style="display: none;">
							<div class="loading-dots">
								<span></span>
								<span></span>
								<span></span>
							</div>
							<span>UIOrbit is thinking...</span>
							<button id="stop-button" class="action-button" style="margin-left: auto;" title="Stop generation">
								<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
									<path stroke-linecap="round" stroke-linejoin="round" d="M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
									<path stroke-linecap="round" stroke-linejoin="round" d="M9 9l6 6m0-6l-6 6" />
								</svg>
							</button>
						</div>

						<!-- Input Area -->
						<div class="input-area">
							<div class="input-container">
								<!-- File Drop Zone -->
								<div id="file-drop-zone" class="file-drop-zone">
									📎 Drop files here to attach
								</div>

								<!-- File Attachments -->
								<div id="file-attachments" class="file-attachments" style="display: none;"></div>

								<!-- @ Mention Dropdown -->
								<div id="mention-dropdown" class="mention-dropdown" style="display: none;"></div>

								<!-- Input Field -->
								<textarea
									id="message-input"
									class="input-field"
									placeholder="Ask UIOrbit to create UI components, analyze designs, or help with frontend development..."
									rows="1"
								></textarea>

								<!-- Input Actions -->
								<div class="input-actions">
									<button id="attach-file" class="action-button" title="Attach files">
										<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
											<path stroke-linecap="round" stroke-linejoin="round" d="M18.375 12.739l-7.693 7.693a4.5 4.5 0 01-6.364-6.364l10.94-10.94A3 3 0 1119.5 7.372L8.552 18.32m.009-.01l-.01.01m5.699-9.941l-7.81 7.81a1.5 1.5 0 002.112 2.13" />
										</svg>
									</button>

									<button id="send-message" class="action-button primary" title="Send message">
										<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
											<path stroke-linecap="round" stroke-linejoin="round" d="M6 12L3.269 3.126A59.768 59.768 0 0121.485 12 59.77 59.77 0 013.27 20.876L5.999 12zm0 0h7.5" />
										</svg>
									</button>
								</div>

								<!-- Hidden File Input -->
								<input type="file" id="file-input" multiple style="display: none;" accept="image/*,.fig,.sketch,.pdf,.json,.js,.ts,.jsx,.tsx,.vue,.svelte,.css,.scss,.less">
							</div>
						</div>
					</div>
				</div>
				</div>

				<script nonce="${nonce}" src="${scriptUri}"></script>
			</body>
			</html>`;
	}

	private getRandomId() {
		let text = '';
		const possible = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
		for (let i = 0; i < 32; i++) {
			text += possible.charAt(Math.floor(Math.random() * possible.length));
		}
		return text;
	}

	// New UIOrbit Enhanced Methods
	private async handleNewMessage(message: string, files: any[], mode: string) {
		try {
			// Process the message based on mode
			let processedMessage = message;

			if (mode === 'agent') {
				processedMessage = `[AGENT MODE] ${message}. Please autonomously create and implement the requested UI components.`;
			}

			// Add file context if files are attached
			if (files && files.length > 0) {
				const fileContext = files.map(f => `File: ${f.name} (${f.type})`).join(', ');
				processedMessage += `\n\nAttached files: ${fileContext}`;
			}

			// Send to AI API
			this.sendApiRequest(processedMessage, { command: "freeText" });
		} catch (error) {
			this.sendMessage({
				type: 'error',
				message: 'Failed to process message: ' + (error as Error).message
			});
		}
	}

	private async indexCodebase() {
		try {
			const workspaceFolder = vscode.workspace.workspaceFolders?.[0];
			if (!workspaceFolder) {
				this.sendMessage({
					type: 'codebaseSummary',
					summary: 'No workspace folder found'
				});
				return;
			}

			// Generate codebase summary
			const summary = await this.generateCodebaseSummary(workspaceFolder.uri.fsPath);

			this.sendMessage({
				type: 'codebaseSummary',
				summary: summary
			});
		} catch (error) {
			this.sendMessage({
				type: 'codebaseSummary',
				summary: 'Failed to index codebase: ' + (error as Error).message
			});
		}
	}

	private async generateCodebaseSummary(workspacePath: string): Promise<string> {
		try {
			// Use AST analyzer to get project structure
			const projectInfo = await this.astAnalyzer.analyzeProject();

			const summary = `
				📁 Project: ${path.basename(workspacePath)}
				🔧 Framework: ${projectInfo.framework}
				📄 Files: ${projectInfo.fileStructure?.length || 0} files analyzed
				⚛️ Components: ${projectInfo.components?.length || 0} UI components found
				🎨 Styling: ${projectInfo.stylingFramework}
			`.replace(/\t/g, '').trim();

			return summary;
		} catch (error) {
			return `Project indexed. Ready to help with UI development.`;
		}
	}

	private detectFramework(workspacePath: string): string {
		try {
			const packageJsonPath = path.join(workspacePath, 'package.json');
			if (fs.existsSync(packageJsonPath)) {
				const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));
				const deps = { ...packageJson.dependencies, ...packageJson.devDependencies };

				if (deps.react) { return 'React'; }
				if (deps.vue) { return 'Vue'; }
				if (deps['@angular/core']) { return 'Angular'; }
				if (deps.svelte) { return 'Svelte'; }
				if (deps.next) { return 'Next.js'; }
				if (deps.nuxt) { return 'Nuxt.js'; }
			}
			return 'Vanilla JS';
		} catch {
			return 'Unknown';
		}
	}

	private detectStylingFramework(workspacePath: string): string {
		try {
			const packageJsonPath = path.join(workspacePath, 'package.json');
			if (fs.existsSync(packageJsonPath)) {
				const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));
				const deps = { ...packageJson.dependencies, ...packageJson.devDependencies };

				if (deps.tailwindcss) { return 'Tailwind CSS'; }
				if (deps['styled-components']) { return 'Styled Components'; }
				if (deps['@emotion/react']) { return 'Emotion'; }
				if (deps.sass || deps.scss) { return 'Sass/SCSS'; }
				if (deps.less) { return 'Less'; }
			}
			return 'CSS';
		} catch {
			return 'CSS';
		}
	}

	private async getMentionSuggestions(query: string) {
		try {
			const workspaceFolder = vscode.workspace.workspaceFolders?.[0];
			if (!workspaceFolder) {
				this.sendMessage({ type: 'mentionSuggestions', suggestions: [] });
				return;
			}

			// Get file suggestions using semantic search
			const searchResults = await this.vectorDb.semanticSearch(query, 10);

			// Format suggestions for UI
			const formattedSuggestions = searchResults
				.filter(result => result.type === 'context')
				.map(result => {
					const context = result.item as any;
					return {
						name: path.basename(context.filePath || context.name || 'Unknown'),
						path: context.filePath || context.name || '',
						icon: this.getFileIcon(context.filePath || context.name || '')
					};
				});

			this.sendMessage({
				type: 'mentionSuggestions',
				suggestions: formattedSuggestions.slice(0, 10) // Limit to 10 suggestions
			});
		} catch (error) {
			this.sendMessage({ type: 'mentionSuggestions', suggestions: [] });
		}
	}

	private getFileIcon(filePath: string): string {
		const ext = path.extname(filePath).toLowerCase();
		switch (ext) {
			case '.js': case '.jsx': return '📜';
			case '.ts': case '.tsx': return '📘';
			case '.vue': return '💚';
			case '.svelte': return '🧡';
			case '.css': case '.scss': case '.sass': case '.less': return '🎨';
			case '.html': return '🌐';
			case '.json': return '📋';
			case '.md': return '📝';
			case '.png': case '.jpg': case '.jpeg': case '.gif': case '.svg': return '🖼️';
			default: return '📄';
		}
	}

	public dispose(): void {
		// Clean up UIOrbit services
		if (this.fileWatcher) {
			this.fileWatcher.dispose();
		}

		// Clean up vector database
		if (this.vectorDb) {
			this.vectorDb.cleanup();
		}
	}
}
